# AI Studio GenerateContent 数据转发指南

## 🎯 功能概述

现在已经实现了将 AI Studio 的 `generateContentUrl` 监听内容正确转发到 Python 后端的功能。系统会自动：

1. **拦截** AI Studio 的流式响应数据
2. **转发** 原始数据到 Python 后端
3. **解析** 数据并提取消息内容
4. **保存** 原始数据和解析结果
5. **广播** 解析后的内容到连接的客户端

## 🏗️ 架构说明

### 前端 (TypeScript Userscript)
- `src/api/request-interceptor.ts`: 拦截 `generateContentUrl` 请求
- `userscripts/aistudio.user.js`: 原始 JavaScript 版本
- 自动转发数据到 `http://localhost:8000/aistudio/save_message`

### 后端 (Python FastAPI)
- `server/api/aistudio.py`: 新的 AI Studio API 处理器
  - `PartialParser`: 基于 `parse_lines.py` 的数据解析器
  - `/aistudio/save_message`: 接收和处理数据的端点
  - `/aistudio/messages`: 获取解析后的消息
- `aistudio.py`: 兼容性服务器 (端口 8001)

## 🚀 使用方法

### 1. 启动服务器

```bash
# 启动主服务器 (推荐)
python -m server

# 或者启动兼容性服务器 (可选)
python aistudio.py
```

### 2. 安装 Userscript

选择以下任一方式：

**方式 A: TypeScript 版本 (推荐)**
```bash
npm run build
# 安装 dist/gemini2api.user.js 到 Tampermonkey
```

**方式 B: JavaScript 版本**
```bash
# 直接安装 userscripts/aistudio.user.js 到 Tampermonkey
```

### 3. 使用 AI Studio

1. 访问 `https://aistudio.google.com`
2. 开始对话
3. 数据将自动转发到 Python 后端

### 4. 验证功能

```bash
# 运行测试脚本
python test_aistudio_forwarding.py
```

## 📡 API 端点

### POST /aistudio/save_message
接收 AI Studio 的流式数据

**请求体:**
```json
"[[[[[[None,\"消息内容\"]]],\"model\"]]],...]"
```

**响应:**
```json
{
  "message": "数据保存成功"
}
```

### GET /aistudio/messages
获取解析后的消息列表

**响应:**
```json
{
  "messages": [
    {
      "content": "解析后的消息内容",
      "is_done": false,
      "timestamp": "2024-01-01T12:00:00"
    }
  ],
  "total": 1
}
```

## 🔧 数据解析逻辑

基于 `parse_lines.py` 的解析逻辑：

```python
class PartialParser:
    def extract_message(self, data):
        """提取消息内容和完成状态"""
        message_list, *is_done = data[0][0]
        if message_list[1] == "model":
            return message_list[0][0][1], is_done == [1]
        return "", False
    
    def feed(self, chunk):
        """处理数据块并返回消息列表"""
        # 处理 JSON 格式的流式数据
        # 返回 [(content, is_done), ...] 列表
```

## 📁 文件说明

- `saved_messages.txt`: 新服务器保存的原始数据
- `saved.txt`: 兼容性服务器保存的数据

## 🐛 故障排除

### 1. 数据未转发
- 检查服务器是否运行: `curl http://localhost:8000/aistudio/save_message`
- 检查 userscript 是否正确安装和启用
- 查看浏览器控制台是否有错误

### 2. 解析失败
- 检查数据格式是否正确
- 查看服务器日志中的错误信息
- 使用测试脚本验证端点功能

### 3. 兼容性问题
- TypeScript 版本使用端口 8000
- JavaScript 版本可以使用端口 8000 或 8001
- 确保端口没有被其他程序占用

## 🔄 数据流程

```
AI Studio 页面
    ↓ (XHR/Fetch 拦截)
Userscript (TypeScript/JavaScript)
    ↓ (HTTP POST)
Python 后端 (/aistudio/save_message)
    ↓ (解析)
PartialParser
    ↓ (保存 + 广播)
文件存储 + WebSocket 客户端
```

## 📊 监控和调试

1. **服务器日志**: 查看解析过程和错误信息
2. **浏览器控制台**: 查看 userscript 的转发状态
3. **测试脚本**: 验证各个端点的功能
4. **文件检查**: 确认数据是否正确保存

## 🎉 完成！

现在您可以：
- ✅ 自动拦截 AI Studio 的流式响应
- ✅ 将数据转发到 Python 后端
- ✅ 解析并提取消息内容
- ✅ 保存原始数据和解析结果
- ✅ 通过 API 获取解析后的消息
- ✅ 兼容原有的测试脚本
