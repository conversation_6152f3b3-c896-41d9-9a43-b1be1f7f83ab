#!/usr/bin/env python3
"""
AI Studio 数据转发测试脚本

测试 generateContentUrl 监听数据是否正确转发到 Python 端
"""

import json
import requests
import time
from pathlib import Path


def test_save_message_endpoint():
    """测试保存消息端点"""
    print("🧪 测试 AI Studio 数据转发端点...")
    
    # 模拟 AI Studio 的流式响应数据
    test_data = '''[[
    [[[[[[None,"THINK",None,None,None,None,None,None,None,None,None,None,1]],"model"]]],None,[477,None,551,None,[[1,219],[2,258]],None,None,None,None,74]],
    [[[[[[None,"这是一个测试消息"]],"model"]]],None,[477,4,1569,None,[[1,219],[2,258]],None,None,None,None,1088]],
    [[[[[[None,"继续的消息内容"]],"model"],1]],None,[477,599,2164,None,[[1,219],[2,258]],None,None,None,None,1088]],
    [None,None,None,["1747981972492120",5494348,3070878298]]
    ]]'''
    
    try:
        # 测试新的端点
        response = requests.post(
            "http://localhost:8000/aistudio/save_message",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ 新端点测试成功")
            print(f"   响应: {response.json()}")
        else:
            print(f"❌ 新端点测试失败，状态码: {response.status_code}")
            print(f"   响应: {response.text}")
            
    except requests.RequestException as e:
        print(f"❌ 请求失败: {e}")
        print("   请确保服务器正在运行: python -m server")


def test_get_messages_endpoint():
    """测试获取解析后的消息端点"""
    print("\n🧪 测试获取解析后的消息端点...")
    
    try:
        response = requests.get(
            "http://localhost:8000/aistudio/messages",
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ 获取消息端点测试成功")
            print(f"   总消息数: {data.get('total', 0)}")
            
            messages = data.get('messages', [])
            if messages:
                print("   最近的消息:")
                for i, msg in enumerate(messages[-3:], 1):  # 显示最后3条消息
                    content = msg.get('content', '')[:50]
                    is_done = msg.get('is_done', False)
                    print(f"     {i}. {content}... (完成: {is_done})")
            else:
                print("   暂无消息")
                
        else:
            print(f"❌ 获取消息端点测试失败，状态码: {response.status_code}")
            print(f"   响应: {response.text}")
            
    except requests.RequestException as e:
        print(f"❌ 请求失败: {e}")


def test_compatibility_endpoint():
    """测试兼容性端点"""
    print("\n🧪 测试兼容性端点 (端口 8001)...")
    
    test_data = "测试兼容性转发"
    
    try:
        response = requests.post(
            "http://localhost:8001/save_message",
            json=test_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ 兼容性端点测试成功")
            print(f"   响应: {response.json()}")
        else:
            print(f"❌ 兼容性端点测试失败，状态码: {response.status_code}")
            
    except requests.RequestException as e:
        print(f"❌ 兼容性端点请求失败: {e}")
        print("   请确保兼容性服务器正在运行: python aistudio.py")


def check_saved_files():
    """检查保存的文件"""
    print("\n📁 检查保存的文件...")
    
    files_to_check = [
        "saved_messages.txt",  # 新服务器保存的文件
        "saved.txt"            # 兼容性服务器保存的文件
    ]
    
    for filename in files_to_check:
        file_path = Path(filename)
        if file_path.exists():
            size = file_path.stat().st_size
            print(f"✅ {filename} 存在，大小: {size} 字节")
            
            # 显示最后几行
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    if lines:
                        print(f"   最后一行: {lines[-1].strip()[:100]}...")
                    else:
                        print("   文件为空")
            except Exception as e:
                print(f"   读取文件失败: {e}")
        else:
            print(f"❌ {filename} 不存在")


def main():
    """主测试函数"""
    print("🚀 AI Studio 数据转发测试")
    print("=" * 50)
    
    # 测试各个端点
    test_save_message_endpoint()
    time.sleep(1)
    
    test_get_messages_endpoint()
    time.sleep(1)
    
    test_compatibility_endpoint()
    time.sleep(1)
    
    check_saved_files()
    
    print("\n" + "=" * 50)
    print("📋 测试完成")
    print("\n💡 使用说明:")
    print("1. 启动新服务器: python -m server")
    print("2. (可选) 启动兼容性服务器: python aistudio.py")
    print("3. 在 AI Studio 页面使用 userscript 进行对话")
    print("4. 数据将自动转发到 Python 后端进行解析")


if __name__ == "__main__":
    main()
