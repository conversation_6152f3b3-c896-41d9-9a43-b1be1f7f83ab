"""
AI Studio 2 API - AI Studio Specific API Handlers

处理 AI Studio 特定的 API 请求
"""

import json
import logging
import uuid
from datetime import datetime
from pathlib import Path
from typing import Any, AsyncGenerator, Dict, List, Optional

from fastapi import HTTPException, Request
from fastapi.responses import J<PERSON><PERSON>esponse, StreamingResponse

from server.core.connection_manager import ConnectionManager
from server.core.models import (
    ChatCompletionRequest,
    ChatCompletionResponse,
    ChatCompletionStreamResponse,
    Choice,
    Message,
    Model,
    Usage,
)

logger = logging.getLogger(__name__)

# AI Studio 模型映射
AISTUDIO_MODEL_MAPPING = {
    "gemini-2.0-flash": "models/gemini-2.0-flash",
    "gemini-2.5-flash": "models/gemini-2.5-flash",
    "gemini-2.5-pro": "models/gemini-2.5-pro",
    "gemini-2.0-exp-advanced": "models/gemini-2.0-exp-advanced",
    "gemini-2.5-exp-advanced": "models/gemini-2.5-exp-advanced",
}

# 反向映射
REVERSE_MODEL_MAPPING = {v: k for k, v in AISTUDIO_MODEL_MAPPING.items()}


class PartialParser:
    """
    AI Studio 流式响应解析器
    基于 parse_lines.py 的逻辑，用于解析 GenerateContent 的流式响应
    """

    def __init__(self):
        self.first_loaded = False
        self.reach_end = False
        self.buffer = ""

    def extract_message(self, data: List[Any]) -> tuple[str, bool]:
        """提取消息内容和是否完成标志"""
        try:
            message_list, *is_done = data[0][0]
            if len(message_list) >= 2 and message_list[1] == "model":
                return message_list[0][0][1], is_done == [1]
            return "", False
        except (IndexError, TypeError) as e:
            logger.debug(f"提取消息失败: {e}")
            return "", False

    def feed(self, chunk: str) -> List[tuple[str, bool]]:
        """
        处理新的数据块
        返回 (消息内容, 是否完成) 的列表
        """
        try:
            increment = chunk[len(self.buffer) :]
            self.buffer = chunk

            if not self.first_loaded:
                # 移除多余的 "[[", 并标记为已加载
                increment = increment[2:] if increment.startswith("[[") else increment
                self.first_loaded = True
            else:
                # 会多一个逗号
                increment = increment[1:] if increment.startswith(",") else increment

            if increment.endswith("]]]]"):
                # 判断是否是最后一块数据
                # 移除末尾的 "]]]]", 并标记为结束
                increment = increment[:-2]
                self.reach_end = True

            # 包裹一层[]，尝试解析 JSON
            increment_data = json.loads("[" + increment + "]")

            if self.reach_end:
                increment_data = increment_data[:-1]

            return [self.extract_message(data) for data in increment_data if data]

        except json.JSONDecodeError as e:
            logger.debug(f"JSON 解析失败: {e}")
            return []
        except Exception as e:
            logger.error(f"数据处理失败: {e}")
            return []


class AiStudioAPIHandler:
    """AI Studio API 处理器"""

    def __init__(self, connection_manager: ConnectionManager):
        self.connection_manager = connection_manager
        self.pending_requests: Dict[str, Dict[str, Any]] = {}
        self.stream_parsers: Dict[str, PartialParser] = {}
        self.saved_messages_file = "saved_messages.txt"

    async def create_chat_completion(
        self, request: ChatCompletionRequest, user_id: str = "default"
    ) -> ChatCompletionResponse | StreamingResponse:
        """创建聊天完成"""

        # 生成请求ID
        request_id = f"aistudio-{uuid.uuid4().hex[:8]}"

        # 验证模型
        model_id = self.normalize_model_id(request.model)
        if not model_id:
            raise HTTPException(status_code=400, detail=f"不支持的模型: {request.model}")

        # 准备请求数据
        request_data = {
            "model": model_id,
            "messages": [msg.model_dump() for msg in request.messages],
            "stream": request.stream or False,
            "temperature": request.temperature,
            "maxTokens": request.max_tokens,
            "topP": request.top_p,
            "stopSequences": request.stop
            if isinstance(request.stop, list)
            else [request.stop]
            if request.stop
            else None,
        }

        # 存储请求信息
        self.pending_requests[request_id] = {
            "request": request,
            "user_id": user_id,
            "created_at": datetime.now(),
            "completed": False,
            "response_data": None,
            "error": None,
        }

        logger.info(f"创建 AI Studio 聊天完成请求: {request_id}, 模型: {model_id}")

        try:
            # 发送请求到用户脚本
            await self.send_request_to_userscript(request_id, request_data)

            if request.stream:
                return StreamingResponse(
                    self.stream_response(request_id), media_type="text/plain", headers={"Cache-Control": "no-cache"}
                )
            else:
                return await self.wait_for_response(request_id)

        except Exception as e:
            # 清理请求
            self.pending_requests.pop(request_id, None)
            logger.error(f"处理聊天完成请求失败: {e}")
            raise HTTPException(status_code=500, detail=str(e))

    async def send_request_to_userscript(self, request_id: str, request_data: Dict[str, Any]) -> None:
        """发送请求到用户脚本"""

        message = {
            "type": "api_request",
            "requestId": request_id,
            "data": request_data,
            "timestamp": datetime.now().isoformat(),
        }

        # 广播到所有连接的用户脚本
        await self.connection_manager.broadcast_to_userscripts(json.dumps(message))

        logger.debug(f"已发送请求到用户脚本: {request_id}")

    async def wait_for_response(self, request_id: str, timeout: int = 300) -> ChatCompletionResponse:
        """等待非流式响应"""
        import asyncio

        start_time = datetime.now()

        while True:
            # 检查超时
            if (datetime.now() - start_time).total_seconds() > timeout:
                self.pending_requests.pop(request_id, None)
                raise HTTPException(status_code=408, detail="请求超时")

            # 检查请求状态
            request_info = self.pending_requests.get(request_id)
            if not request_info:
                raise HTTPException(status_code=404, detail="请求不存在")

            if request_info["error"]:
                self.pending_requests.pop(request_id, None)
                raise HTTPException(status_code=500, detail=request_info["error"])

            if request_info["completed"]:
                response_data = request_info.get("response_data", {})
                self.pending_requests.pop(request_id, None)

                return self.create_completion_response(
                    request_info["request"],
                    response_data.get("content", ""),
                    response_data.get("finishReason", "stop"),
                    response_data.get("usage"),
                )

            # 短暂等待
            await asyncio.sleep(0.1)

    async def stream_response(self, request_id: str) -> AsyncGenerator[str, None]:
        """流式响应生成器"""
        import asyncio

        start_time = datetime.now()
        timeout = 300  # 5分钟超时

        try:
            while True:
                # 检查超时
                if (datetime.now() - start_time).total_seconds() > timeout:
                    yield self.create_error_chunk("请求超时")
                    break

                # 检查请求状态
                request_info = self.pending_requests.get(request_id)
                if not request_info:
                    yield self.create_error_chunk("请求不存在")
                    break

                if request_info["error"]:
                    yield self.create_error_chunk(request_info["error"])
                    break

                if request_info["completed"]:
                    # 发送结束标记
                    yield self.create_stream_end_chunk()
                    break

                # 短暂等待
                await asyncio.sleep(0.1)

        finally:
            # 清理请求
            self.pending_requests.pop(request_id, None)

    def handle_userscript_message(self, message: Dict[str, Any]) -> None:
        """处理来自用户脚本的消息"""

        message_type = message.get("type")
        request_id = message.get("requestId")

        if not request_id or request_id not in self.pending_requests:
            logger.warning(f"收到未知请求ID的消息: {request_id}")
            return

        request_info = self.pending_requests[request_id]

        try:
            if message_type == "api_response":
                # 非流式响应
                request_info["response_data"] = message.get("data", {})
                request_info["completed"] = True
                logger.info(f"收到 API 响应: {request_id}")

            elif message_type == "api_stream_chunk":
                # 流式响应块
                self.handle_stream_chunk(request_id, message.get("data", {}))

            elif message_type == "api_stream_end":
                # 流式响应结束
                request_info["completed"] = True
                logger.info(f"流式响应结束: {request_id}")

            elif message_type == "api_error":
                # 错误响应
                request_info["error"] = message.get("error", "未知错误")
                request_info["completed"] = True
                logger.error(f"API 请求错误: {request_id}, {request_info['error']}")

        except Exception as e:
            logger.error(f"处理用户脚本消息失败: {e}")
            request_info["error"] = str(e)
            request_info["completed"] = True

    def handle_stream_chunk(self, request_id: str, data: Dict[str, Any]) -> None:
        """处理流式响应块"""

        # 这里可以实现流式数据的缓存和处理
        # 目前简单记录日志
        delta = data.get("delta", {})
        content = delta.get("content", "")

        if content:
            logger.debug(f"收到流式块: {request_id}, 长度: {len(content)}")

    def create_completion_response(
        self,
        request: ChatCompletionRequest,
        content: str,
        finish_reason: str = "stop",
        usage: Optional[Dict[str, Any]] = None,
    ) -> ChatCompletionResponse:
        """创建完成响应"""

        return ChatCompletionResponse(
            id=f"chatcmpl-{uuid.uuid4().hex[:8]}",
            object="chat.completion",
            created=int(datetime.now().timestamp()),
            model=request.model,
            choices=[Choice(index=0, message=Message(role="assistant", content=content), finish_reason=finish_reason)],
            usage=Usage(
                prompt_tokens=usage.get("promptTokenCount", 0) if usage else 0,
                completion_tokens=usage.get("candidatesTokenCount", 0) if usage else 0,
                total_tokens=usage.get("totalTokenCount", 0) if usage else 0,
            )
            if usage
            else None,
        )

    def create_stream_chunk(
        self, request: ChatCompletionRequest, content: str, finish_reason: Optional[str] = None
    ) -> str:
        """创建流式响应块"""

        chunk = ChatCompletionStreamResponse(
            id=f"chatcmpl-{uuid.uuid4().hex[:8]}",
            object="chat.completion.chunk",
            created=int(datetime.now().timestamp()),
            model=request.model,
            choices=[Choice(index=0, delta={"content": content} if content else {}, finish_reason=finish_reason)],
        )

        return f"data: {chunk.model_dump_json()}\n\n"

    def create_stream_end_chunk(self) -> str:
        """创建流式响应结束块"""
        return "data: [DONE]\n\n"

    def create_error_chunk(self, error: str) -> str:
        """创建错误块"""
        error_data = {"error": {"message": error, "type": "api_error", "code": "internal_error"}}
        return f"data: {json.dumps(error_data)}\n\n"

    def normalize_model_id(self, model: str) -> Optional[str]:
        """标准化模型ID"""

        # 直接匹配
        if model in AISTUDIO_MODEL_MAPPING:
            return AISTUDIO_MODEL_MAPPING[model]

        # 反向匹配
        if model in REVERSE_MODEL_MAPPING:
            return model

        # 模糊匹配
        model_lower = model.lower()
        for key, value in AISTUDIO_MODEL_MAPPING.items():
            if key.lower() in model_lower or model_lower in key.lower():
                return value

        return None

    def get_available_models(self) -> List[Model]:
        """获取可用模型列表"""

        models = []
        for model_id, _ in AISTUDIO_MODEL_MAPPING.items():
            models.append(
                Model(id=model_id, object="model", created=int(datetime.now().timestamp()), owned_by="google")
            )

        return models

    def cleanup_expired_requests(self, max_age_seconds: int = 3600) -> None:
        """清理过期请求"""

        current_time = datetime.now()
        expired_requests = []

        for request_id, request_info in self.pending_requests.items():
            age = (current_time - request_info["created_at"]).total_seconds()
            if age > max_age_seconds:
                expired_requests.append(request_id)

        for request_id in expired_requests:
            self.pending_requests.pop(request_id, None)
            logger.info(f"清理过期请求: {request_id}")

        if expired_requests:
            logger.info(f"清理了 {len(expired_requests)} 个过期请求")

    async def save_generate_content_data(self, request: Request) -> JSONResponse:
        """
        保存 GenerateContent 的流式数据
        对应原来的 /save_message 端点
        """
        try:
            # 获取请求数据
            if request.headers.get("content-type") == "application/json":
                data = await request.json()
                message_data = data if isinstance(data, str) else json.dumps(data)
            else:
                body = await request.body()
                message_data = body.decode("utf-8")

            # 保存原始数据到文件
            with open(self.saved_messages_file, "a", encoding="utf-8") as f:
                f.write(message_data + "\n")

            logger.info(f"保存 GenerateContent 数据，长度: {len(message_data)}")

            # 尝试解析流式数据
            await self.process_stream_data(message_data)

            return JSONResponse({"message": "数据保存成功"})

        except Exception as e:
            logger.error(f"保存 GenerateContent 数据失败: {e}")
            return JSONResponse({"error": f"保存数据失败: {str(e)}"}, status_code=500)

    async def process_stream_data(self, raw_data: str) -> None:
        """
        处理流式数据，解析并提取消息内容
        """
        try:
            # 创建解析器实例
            parser = PartialParser()

            # 解析数据
            messages = parser.feed(raw_data)

            for message_content, is_done in messages:
                if message_content:
                    logger.info(f"解析到消息内容: {message_content[:100]}...")

                    # 这里可以进一步处理消息内容
                    # 例如：广播到连接的客户端、触发回调等
                    await self.handle_parsed_message(message_content, is_done)

        except Exception as e:
            logger.error(f"处理流式数据失败: {e}")

    async def handle_parsed_message(self, content: str, is_done: bool) -> None:
        """
        处理解析后的消息内容
        """
        try:
            # 构造消息
            message = {
                "type": "stream_content",
                "content": content,
                "is_done": is_done,
                "timestamp": datetime.now().isoformat(),
            }

            # 广播到所有连接的客户端
            await self.connection_manager.broadcast_to_userscripts(json.dumps(message))

            logger.debug(f"广播流式内容，长度: {len(content)}, 完成: {is_done}")

        except Exception as e:
            logger.error(f"处理解析后的消息失败: {e}")

    async def get_parsed_messages(self) -> JSONResponse:
        """
        获取解析后的消息内容
        类似于 parse_lines.py 的功能
        """
        try:
            if not Path(self.saved_messages_file).exists():
                return JSONResponse({"messages": [], "total": 0})

            # 读取保存的数据
            with open(self.saved_messages_file, encoding="utf-8") as f:
                lines = f.read().splitlines()

            # 解析所有数据
            all_messages = []
            parser = PartialParser()

            for line in lines:
                if line.strip():
                    messages = parser.feed(line.strip())
                    for content, is_done in messages:
                        if content:
                            all_messages.append(
                                {"content": content, "is_done": is_done, "timestamp": datetime.now().isoformat()}
                            )

            logger.info(f"解析完成，共 {len(all_messages)} 条消息")

            return JSONResponse({"messages": all_messages, "total": len(all_messages)})

        except Exception as e:
            logger.error(f"获取解析后的消息失败: {e}")
            return JSONResponse({"error": f"获取消息失败: {str(e)}"}, status_code=500)
